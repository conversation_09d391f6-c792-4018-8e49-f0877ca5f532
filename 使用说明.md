# AI4DB实验使用说明

## 快速开始

### 1. 环境准备

1. **安装Python依赖**
   ```bash
   pip install -r requirements_fixed.txt
   ```

2. **配置数据库**
   - 确保MySQL服务正在运行
   - 修改`setup_database.py`中的数据库密码
   - 运行数据库初始化脚本：
   ```bash
   python setup_database.py
   ```

3. **获取API密钥**
   - Vanna API密钥：访问 https://vanna.ai/account/models
   - Gemini API密钥：访问 https://aistudio.google.com/apikey

4. **配置API密钥**
   - 复制`config_template.py`为`config.py`
   - 在`config.py`中填入您的API密钥和数据库信息

### 2. 运行实验

1. **修改主程序配置**
   在`ai4db_experiment.py`文件开头，填入您的配置信息：
   ```python
   VANNA_API_KEY = "your_vanna_api_key"
   VANNA_MODEL_BASIC = "your_basic_model_name"
   VANNA_MODEL_QUESTION = "your_question_model_name"
   VANNA_MODEL_QUERY = "your_query_model_name"
   GEMINI_API_KEY = "your_gemini_api_key"
   DB_CONFIG["password"] = "your_mysql_password"
   ```

2. **运行完整实验**
   ```bash
   python ai4db_experiment.py
   ```

3. **查看结果**
   实验完成后，会生成以下文件：
   - `vanna_answer.json` - 原始Vanna模型结果
   - `gemini_answer.json` - Gemini模型结果
   - `basic_answer.json` - 基础训练后的结果
   - `question_answer.json` - 问题相似训练后的结果
   - `query_answer.json` - 查询相似训练后的结果

## 文件说明

### 核心文件
- `ai4db_experiment.py` - 主实验程序
- `setup_database.py` - 数据库初始化脚本
- `config_template.py` - 配置文件模板

### 训练数据
- `basic_train.json` - 基础DDL训练数据
- `question_train.json` - 问题相似训练数据（已扩展）
- `query_train.json` - 查询相似训练数据（已扩展）

### 结果文件
- `*_answer.json` - 各阶段实验结果
- `report7.md` - 实验报告

### 依赖文件
- `requirements_fixed.txt` - Python依赖包列表

## 注意事项

### 网络配置
如果需要访问Gemini API，可能需要配置代理：
```python
import os
os.environ['http_proxy'] = 'http://127.0.0.1:7890'
os.environ['https_proxy'] = 'http://127.0.0.1:7890'
```

### Vanna模型创建
1. 访问 https://vanna.ai/account/models
2. 创建三个不同的模型：
   - 基础训练模型
   - 问题相似训练模型
   - 查询相似训练模型

### 数据库数据
- 脚本只提供了示例数据
- 完整的MovieLens数据集请从以下地址下载：
  https://files.grouplens.org/datasets/movielens/ml-latest.zip

## 实验步骤详解

### 步骤1：原始模型测试
- 测试未训练的Vanna和Gemini模型
- 记录SQL生成和执行性能
- 建立性能基准

### 步骤2：基础训练
- 使用DDL训练Vanna模型
- 让模型了解表结构
- 提升基础SQL生成能力

### 步骤3：问题相似训练
- 使用问题-SQL对训练模型
- 提升自然语言理解能力
- 增强查询意图识别

### 步骤4：查询相似训练
- 使用SQL结构相似的数据训练
- 提升复杂查询构建能力
- 优化查询性能意识

### 步骤5：结果分析
- 对比不同训练阶段的效果
- 分析训练数据的影响
- 与成熟模型进行对比

## 常见问题

### Q: Vanna连接失败
A: 检查API密钥是否正确，确保网络连接正常

### Q: Gemini访问失败
A: 确保已配置代理，检查API密钥是否有效

### Q: 数据库连接失败
A: 检查MySQL服务是否启动，密码是否正确

### Q: 训练数据加载失败
A: 确保JSON文件格式正确，编码为UTF-8

## 扩展功能

### 自定义测试问题
在`ai4db_experiment.py`中修改`test_questions`列表：
```python
self.test_questions = [
    "您的自定义问题1",
    "您的自定义问题2",
    # ...
]
```

### 添加训练数据
在相应的JSON文件中添加新的训练样本：
```json
{
    "sql": "您的SQL语句",
    "question": "对应的问题描述"
}
```

### 修改评估指标
在相应的方法中添加新的性能指标计算逻辑。

## 技术支持

如果遇到问题，请检查：
1. Python版本是否为3.8+
2. 所有依赖包是否正确安装
3. API密钥是否有效
4. 网络连接是否正常
5. 数据库服务是否启动

更多技术细节请参考实验报告和源代码注释。
