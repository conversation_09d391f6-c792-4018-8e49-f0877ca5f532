[{"sql": "SELECT m.title \nFROM movies m \nJOIN (\n  SELECT movieId, AVG(CAST(rating AS DECIMAL(3,1)) avg \n  FROM ratings \n  GROUP BY movieId \n  ORDER BY avg DESC \n  LIMIT 10\n) t ON m.movieId = t.movieId;", "question": "获取用户评价均值排名前十的影片名称"}, {"sql": "SELECT title FROM movies \nWHERE movieId IN (\n  SELECT movieId FROM ratings \n  GROUP BY movieId \n  ORDER BY AVG(rating+0.0) DESC \n  LIMIT 10\n);", "question": "筛选评分平均值最高的10部电影"}, {"sql": "SELECT m.title \nFROM movies m \nCROSS JOIN LATERAL (\n  SELECT AVG(rating) avg \n  FROM ratings \n  WHERE movieId = m.movieId \n  ORDER BY avg DESC \n  LIMIT 10\n) t;", "question": "查询评分TOP10的电影标题"}, {"sql": "SELECT userId \nFROM ratings \nGROUP BY userId \nORDER BY COUNT(*) DESC \nLIMIT 1;", "question": "查找最活跃的评分用户编号"}, {"sql": "SELECT userId \nFROM ratings \nGROUP BY userId \nHAVING COUNT(*) = (\n  SELECT MAX(cnt) \n  FROM (\n    SELECT COUNT(*) cnt \n    FROM ratings \n    GROUP BY userId\n  ) t\n);", "question": "确定发表评分最多的用户ID"}, {"sql": "SET @max_count := 0;\nSELECT userId \nFROM (\n  SELECT userId, @cnt := COUNT(*) cnt,\n    @max_count := GREATEST(@max_count, @cnt)\n  FROM ratings \n  GROUP BY userId\n) t \nWHERE cnt = @max_count;", "question": "统计评分次数冠军用户ID"}, {"sql": "SELECT title, imdbId \nFROM movies NATURAL JOIN links \nWHERE genres REGEXP '\\\\bAction\\\\b';", "question": "显示动作类影片名称与IMDB编码"}, {"sql": "'SELECT DISTINCT m.title, l.imdbId  \nFROM movies m  \nFORCE INDEX (genres_index)  \nJOIN links l ON m.movieId = l.movieId  \nWHERE m.genres LIKE 'Action%'  \n  OR m.genres LIKE '%|Action%';", "question": "检索属于动作类型的电影标题及其IMDB标识符"}, {"sql": "SELECT m.title, l.imdbId  \nFROM movies m  \nUSE INDEX (movieId_idx)  \nJOIN links l USING(movieId)  \nWHERE SUBSTRING_INDEX(genres, '|', 1) = 'Action'  \n   OR genres LIKE '%|Action|%'  \n   OR genres LIKE '%|Action';", "question": "获取包含Action分类的电影名称与IMDB ID"}, {"sql": "SELECT m.title, \n  ROUND(AVG(r.rating+0.0),2) avg_rating,\n  COUNT(r.userId) rating_count \nFROM movies m \nLEFT JOIN ratings r USING(movieId) \nGROUP BY m.movieId;", "question": "统计所有影片的评分均值与评价总量"}, {"sql": "SELECT DISTINCT m.title, \n  AVG(r.rating+0.0) OVER(PARTITION BY m.movieId) avg_rating,\n  COUNT(r.userId) OVER(PARTITION BY m.movieId) rating_count \nFROM movies m \nLEFT JOIN ratings r USING(movieId);", "question": "生成电影评分统计数据（均值+总数）"}, {"sql": "SELECT m.title, \n  JSON_OBJECT(\n    'avg_rating', COALESCE(AVG(r.rating+0.0),0),\n    'rating_count', COUNT(r.rating)\n  ) AS stats \nFROM movies m \nLEFT JOIN ratings r USING(movieId) \nGROUP BY m.movieId;", "question": "输出每部电影的用户评分统计报告"}, {"sql": "SELECT m.title \nFROM movies m \nLEFT JOIN ratings r USING(movieId) \nWHERE r.movieId IS NULL;", "question": "查询零评价记录的影片清单"}, {"sql": "SELECT title FROM movies m \nWHERE NOT EXISTS (\n  SELECT 1 FROM ratings r1 WHERE r1.movieId = m.movieId\n) AND NOT EXISTS (\n  SELECT 1 FROM ratings r2 WHERE r2.movieId = m.movieId\n);", "question": "筛选从未被评分的电影名称"}, {"sql": "SELECT title FROM movies \nWHERE (SELECT COUNT(*) FROM ratings \n       WHERE movieId = movies.movieId) = 0;", "question": "检索无用户评价的影片标题"}, {"sql": "SELECT DISTINCT m.title \nFROM movies m \nJOIN links l ON m.movieId = l.movieId \nWHERE l.imdbId IS NOT NULL \n  AND l.tmdbId IS NOT NULL;", "question": "查找双平台标识齐全的影片名称"}, {"sql": "SELECT title FROM movies \nWHERE EXISTS (\n  SELECT 1 FROM links \n  WHERE movieId = movies.movieId \n    AND imdbId IS NOT NULL \n    AND tmdbId IS NOT NULL\n);", "question": "获取具有完整外部标识的电影"}, {"sql": "SELECT title FROM movies \nWHERE movieId IN (\n  SELECT movieId FROM links \n  WHERE NOT (imdbId <=> NULL) \n    AND NOT (tmdbId <=> NULL)\n);", "question": "筛选同时存在两种ID的影片"}, {"sql": "SELECT m.title \nFROM movies m \nJOIN ratings r ON m.movieId = r.movieId \nGROUP BY m.movieId \nORDER BY AVG(CAST(r.rating AS DECIMAL(3,1))) DESC \nLIMIT 5;", "question": "获取评分最高的前5部电影标题"}, {"sql": "SELECT title FROM movies \nWHERE movieId IN (\n  SELECT movieId FROM ratings \n  GROUP BY movieId \n  ORDER BY AVG(rating+0.0) DESC \n  LIMIT 5\n);", "question": "查找评分均值排名前5的影片名称"}, {"sql": "SELECT userId \nFROM ratings \nGROUP BY userId \nORDER BY AVG(CAST(rating AS DECIMAL(3,1))) DESC \nLIMIT 1;", "question": "查找平均评分最高的用户ID"}, {"sql": "SELECT userId \nFROM ratings \nGROUP BY userId \nHAVING AVG(rating+0.0) = (\n  SELECT MAX(avg_rating) \n  FROM (\n    SELECT AVG(rating+0.0) avg_rating \n    FROM ratings \n    GROUP BY userId\n  ) t\n);", "question": "确定给出最高平均评分的用户编号"}, {"sql": "SELECT m.title, l.imdbId \nFROM movies m \nJOIN links l ON m.movieId = l.movieId \nWHERE m.genres LIKE '%Comedy%';", "question": "显示喜剧类影片名称与IMDB编码"}, {"sql": "SELECT DISTINCT m.title, l.imdbId \nFROM movies m \nJOIN links l USING(movieId) \nWHERE m.genres REGEXP '\\\\bComedy\\\\b';", "question": "检索属于喜剧类型的电影标题及其IMDB标识符"}, {"sql": "SELECT m.title, \n  COUNT(r.userId) rating_count \nFROM movies m \nLEFT JOIN ratings r USING(movieId) \nGROUP BY m.movieId \nORDER BY rating_count DESC;", "question": "统计所有影片的评价总量并排序"}, {"sql": "SELECT m.title, \n  COUNT(r.rating) AS total_ratings \nFROM movies m \nLEFT JOIN ratings r USING(movieId) \nGROUP BY m.movieId \nHAVING total_ratings > 0;", "question": "生成电影评分数量统计报告"}, {"sql": "SELECT m.title \nFROM movies m \nLEFT JOIN ratings r USING(movieId) \nWHERE r.movieId IS NULL \nLIMIT 10;", "question": "查询前10部零评价记录的影片清单"}, {"sql": "SELECT title FROM movies m \nWHERE NOT EXISTS (\n  SELECT 1 FROM ratings r WHERE r.movieId = m.movieId\n) \nLIMIT 20;", "question": "筛选前20部从未被评分的电影名称"}]