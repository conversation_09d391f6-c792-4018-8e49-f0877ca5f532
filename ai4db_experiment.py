#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于大语言模型的SQL生成实验
使用Vanna和Google Gemini进行SQL生成和训练
"""

import json
import time
import os
import pymysql
from vanna.remote import VannaDefault
import google.generativeai as genai

# 配置信息 - 需要用户填入自己的API密钥
VANNA_API_KEY = ""  # 请填入您的Vanna API密钥
VANNA_MODEL_BASIC = ""  # 请填入您的基础Vanna模型名称
VANNA_MODEL_QUESTION = ""  # 请填入您的问题相似Vanna模型名称
VANNA_MODEL_QUERY = ""  # 请填入您的查询相似Vanna模型名称

GEMINI_API_KEY = ""  # 请填入您的Gemini API密钥
GEMINI_MODEL = "gemini-pro"  # Gemini模型名称

# 数据库配置 - 需要用户填入自己的数据库信息
DB_CONFIG = {
    "host": "localhost",
    "user": "root",
    "password": "",  # 请填入您的数据库密码
    "database": "movielens",  # 数据库名称
    "charset": "utf8mb4"
}

# 设置代理（如果需要）
# os.environ['http_proxy'] = 'http://127.0.0.1:7890'
# os.environ['https_proxy'] = 'http://127.0.0.1:7890'

class AI4DBExperiment:
    """AI4DB实验主类"""
    
    def __init__(self):
        """初始化实验环境"""
        self.vanna_basic = None
        self.vanna_question = None
        self.vanna_query = None
        self.gemini_model = None
        self.db_connection = None
        
        # 测试问题列表
        self.test_questions = [
            "找出平均评分最高的前10部电影名称",
            "统计每个用户评分数量最多的用户ID", 
            "列出所有Action类型电影的标题及其对应的IMDB ID",
            "计算每部电影的平均评分及评分次数",
            "找出没有任何评分的电影标题",
            "检索同时拥有IMDB ID和TMDB ID的电影标题"
        ]
    
    def setup_apis(self):
        """设置API连接"""
        try:
            # 初始化Vanna模型
            if VANNA_API_KEY and VANNA_MODEL_BASIC:
                self.vanna_basic = VannaDefault(api_key=VANNA_API_KEY, model=VANNA_MODEL_BASIC)
                print("✓ Vanna基础模型连接成功")
            
            if VANNA_API_KEY and VANNA_MODEL_QUESTION:
                self.vanna_question = VannaDefault(api_key=VANNA_API_KEY, model=VANNA_MODEL_QUESTION)
                print("✓ Vanna问题相似模型连接成功")
                
            if VANNA_API_KEY and VANNA_MODEL_QUERY:
                self.vanna_query = VannaDefault(api_key=VANNA_API_KEY, model=VANNA_MODEL_QUERY)
                print("✓ Vanna查询相似模型连接成功")
            
            # 初始化Gemini模型
            if GEMINI_API_KEY:
                genai.configure(api_key=GEMINI_API_KEY)
                self.gemini_model = genai.GenerativeModel(GEMINI_MODEL)
                print("✓ Gemini模型连接成功")
                
        except Exception as e:
            print(f"✗ API连接失败: {e}")
    
    def setup_database(self):
        """设置数据库连接"""
        try:
            self.db_connection = pymysql.connect(**DB_CONFIG)
            print("✓ 数据库连接成功")
        except Exception as e:
            print(f"✗ 数据库连接失败: {e}")
    
    def execute_sql_with_timing(self, sql, description=""):
        """执行SQL并记录时间"""
        if not self.db_connection:
            return None, 0, "数据库未连接"
        
        try:
            cursor = self.db_connection.cursor()
            start_time = time.time()
            cursor.execute(sql)
            result = cursor.fetchall()
            end_time = time.time()
            execution_time = end_time - start_time
            cursor.close()
            
            print(f"✓ {description} - 执行时间: {execution_time:.4f}秒")
            return result, execution_time, "成功"
            
        except Exception as e:
            print(f"✗ {description} - SQL执行失败: {e}")
            return None, 0, str(e)
    
    def generate_sql_with_vanna(self, question, vanna_model, model_name="Vanna"):
        """使用Vanna生成SQL"""
        try:
            if not vanna_model:
                return "", 0, f"{model_name}模型未初始化"
            
            start_time = time.time()
            sql = vanna_model.generate_sql(question)
            end_time = time.time()
            generation_time = end_time - start_time
            
            print(f"✓ {model_name}生成SQL成功 - 生成时间: {generation_time:.4f}秒")
            return sql, generation_time, "成功"
            
        except Exception as e:
            print(f"✗ {model_name}生成SQL失败: {e}")
            return "", 0, str(e)
    
    def generate_sql_with_gemini(self, question):
        """使用Gemini生成SQL"""
        try:
            if not self.gemini_model:
                return "", 0, "Gemini模型未初始化"
            
            # 自定义Prompt
            prompt = f"""
            基于以下数据库表结构生成SQL查询语句：
            
            表结构：
            1. movies表：movieId(电影ID), title(电影标题), genres(电影类型)
            2. ratings表：userId(用户ID), movieId(电影ID), rating(评分), timestamp(时间戳)
            3. links表：movieId(电影ID), imdbId(IMDB ID), tmdbId(TMDB ID)
            
            问题：{question}
            
            请只返回SQL语句，不要包含任何解释。
            """
            
            start_time = time.time()
            response = self.gemini_model.generate_content(prompt)
            sql = response.text.strip()
            end_time = time.time()
            generation_time = end_time - start_time
            
            # 清理SQL语句
            if sql.startswith("```sql"):
                sql = sql[6:]
            if sql.endswith("```"):
                sql = sql[:-3]
            sql = sql.strip()
            
            print(f"✓ Gemini生成SQL成功 - 生成时间: {generation_time:.4f}秒")
            return sql, generation_time, "成功"
            
        except Exception as e:
            print(f"✗ Gemini生成SQL失败: {e}")
            return "", 0, str(e)
    
    def train_vanna_basic(self):
        """训练Vanna基础模型（DDL）"""
        try:
            if not self.vanna_basic:
                print("✗ Vanna基础模型未初始化")
                return
            
            # 加载基础训练数据
            with open('basic_train.json', 'r', encoding='utf-8') as f:
                basic_data = json.load(f)
            
            print("开始基础训练（DDL）...")
            for item in basic_data:
                self.vanna_basic.train(ddl=item['ddl'])
                print(f"✓ 训练DDL: {item['ddl'][:50]}...")
            
            print("✓ 基础训练完成")
            
        except Exception as e:
            print(f"✗ 基础训练失败: {e}")
    
    def train_vanna_question_similar(self):
        """训练Vanna问题相似模型"""
        try:
            if not self.vanna_question:
                print("✗ Vanna问题相似模型未初始化")
                return
            
            # 先训练基础DDL
            with open('basic_train.json', 'r', encoding='utf-8') as f:
                basic_data = json.load(f)
            
            for item in basic_data:
                self.vanna_question.train(ddl=item['ddl'])
            
            # 加载问题相似训练数据
            with open('question_train.json', 'r', encoding='utf-8') as f:
                question_data = json.load(f)
            
            print("开始问题相似训练...")
            for item in question_data:
                self.vanna_question.train(
                    question=item['question'],
                    sql=item['sql']
                )
                print(f"✓ 训练问题: {item['question'][:30]}...")
            
            print("✓ 问题相似训练完成")
            
        except Exception as e:
            print(f"✗ 问题相似训练失败: {e}")
    
    def train_vanna_query_similar(self):
        """训练Vanna查询相似模型"""
        try:
            if not self.vanna_query:
                print("✗ Vanna查询相似模型未初始化")
                return
            
            # 先训练基础DDL
            with open('basic_train.json', 'r', encoding='utf-8') as f:
                basic_data = json.load(f)
            
            for item in basic_data:
                self.vanna_query.train(ddl=item['ddl'])
            
            # 加载查询相似训练数据
            with open('query_train.json', 'r', encoding='utf-8') as f:
                query_data = json.load(f)
            
            print("开始查询相似训练...")
            for item in query_data:
                self.vanna_query.train(
                    question=item['question'],
                    sql=item['sql']
                )
                print(f"✓ 训练查询: {item['question'][:30]}...")
            
            print("✓ 查询相似训练完成")
            
        except Exception as e:
            print(f"✗ 查询相似训练失败: {e}")
    
    def test_original_models(self):
        """测试原始模型（未训练）"""
        print("\n=== 步骤1：原始模型测试 ===")
        
        vanna_results = []
        gemini_results = []
        
        for question in self.test_questions:
            print(f"\n测试问题: {question}")
            
            # 测试原始Vanna
            sql, gen_time, status = self.generate_sql_with_vanna(question, self.vanna_basic, "原始Vanna")
            result, exec_time, exec_status = self.execute_sql_with_timing(sql, "原始Vanna")
            
            vanna_results.append({
                "question": question,
                "sql": sql,
                "generation_time": gen_time,
                "execution_time": exec_time,
                "generation_status": status,
                "execution_status": exec_status
            })
            
            # 测试Gemini
            sql, gen_time, status = self.generate_sql_with_gemini(question)
            result, exec_time, exec_status = self.execute_sql_with_timing(sql, "Gemini")
            
            gemini_results.append({
                "question": question,
                "sql": sql,
                "generation_time": gen_time,
                "execution_time": exec_time,
                "generation_status": status,
                "execution_status": exec_status
            })
        
        # 保存结果
        with open('vanna_answer.json', 'w', encoding='utf-8') as f:
            json.dump(vanna_results, f, ensure_ascii=False, indent=2)
        
        with open('gemini_answer.json', 'w', encoding='utf-8') as f:
            json.dump(gemini_results, f, ensure_ascii=False, indent=2)
        
        print("✓ 原始模型测试完成，结果已保存")
    
    def test_basic_trained_model(self):
        """测试基础训练模型"""
        print("\n=== 步骤2：基础训练模型测试 ===")
        
        results = []
        
        for question in self.test_questions:
            print(f"\n测试问题: {question}")
            
            sql, gen_time, status = self.generate_sql_with_vanna(question, self.vanna_basic, "基础训练Vanna")
            result, exec_time, exec_status = self.execute_sql_with_timing(sql, "基础训练Vanna")
            
            results.append({
                "question": question,
                "sql": sql,
                "generation_time": gen_time,
                "execution_time": exec_time,
                "generation_status": status,
                "execution_status": exec_status
            })
        
        # 保存结果
        with open('basic_answer.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print("✓ 基础训练模型测试完成，结果已保存")
    
    def test_question_similar_model(self):
        """测试问题相似训练模型"""
        print("\n=== 步骤3：问题相似训练模型测试 ===")
        
        results = []
        
        for question in self.test_questions:
            print(f"\n测试问题: {question}")
            
            sql, gen_time, status = self.generate_sql_with_vanna(question, self.vanna_question, "问题相似Vanna")
            result, exec_time, exec_status = self.execute_sql_with_timing(sql, "问题相似Vanna")
            
            results.append({
                "question": question,
                "sql": sql,
                "generation_time": gen_time,
                "execution_time": exec_time,
                "generation_status": status,
                "execution_status": exec_status
            })
        
        # 保存结果
        with open('question_answer.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print("✓ 问题相似训练模型测试完成，结果已保存")
    
    def test_query_similar_model(self):
        """测试查询相似训练模型"""
        print("\n=== 步骤4：查询相似训练模型测试 ===")
        
        results = []
        
        for question in self.test_questions:
            print(f"\n测试问题: {question}")
            
            sql, gen_time, status = self.generate_sql_with_vanna(question, self.vanna_query, "查询相似Vanna")
            result, exec_time, exec_status = self.execute_sql_with_timing(sql, "查询相似Vanna")
            
            results.append({
                "question": question,
                "sql": sql,
                "generation_time": gen_time,
                "execution_time": exec_time,
                "generation_status": status,
                "execution_status": exec_status
            })
        
        # 保存结果
        with open('query_answer.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print("✓ 查询相似训练模型测试完成，结果已保存")
    
    def run_full_experiment(self):
        """运行完整实验"""
        print("开始AI4DB实验...")
        
        # 1. 设置环境
        self.setup_apis()
        self.setup_database()
        
        # 2. 测试原始模型
        self.test_original_models()
        
        # 3. 基础训练并测试
        self.train_vanna_basic()
        self.test_basic_trained_model()
        
        # 4. 问题相似训练并测试
        self.train_vanna_question_similar()
        self.test_question_similar_model()
        
        # 5. 查询相似训练并测试
        self.train_vanna_query_similar()
        self.test_query_similar_model()
        
        print("\n✓ 实验完成！请查看生成的JSON文件获取详细结果。")

def main():
    """主函数"""
    print("AI4DB实验 - 基于大语言模型的SQL生成")
    print("=" * 50)
    
    # 检查配置
    if not VANNA_API_KEY:
        print("⚠️  请在代码中填入您的Vanna API密钥")
        return
    
    if not GEMINI_API_KEY:
        print("⚠️  请在代码中填入您的Gemini API密钥")
        return
    
    if not DB_CONFIG["password"]:
        print("⚠️  请在代码中填入您的数据库密码")
        return
    
    # 运行实验
    experiment = AI4DBExperiment()
    experiment.run_full_experiment()

if __name__ == "__main__":
    main()
