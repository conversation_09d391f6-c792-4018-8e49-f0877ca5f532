#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件模板
请复制此文件为config.py并填入您的实际配置信息
"""

# Vanna配置
VANNA_API_KEY = "your_vanna_api_key_here"  # 请在https://vanna.ai/account/models获取
VANNA_MODEL_BASIC = "your_basic_model_name"  # 基础训练模型名称
VANNA_MODEL_QUESTION = "your_question_model_name"  # 问题相似训练模型名称  
VANNA_MODEL_QUERY = "your_query_model_name"  # 查询相似训练模型名称

# Gemini配置
GEMINI_API_KEY = "your_gemini_api_key_here"  # 请在https://aistudio.google.com/apikey获取
GEMINI_MODEL = "gemini-pro"

# 数据库配置
DB_CONFIG = {
    "host": "localhost",
    "user": "root", 
    "password": "your_database_password",  # 请填入您的MySQL密码
    "database": "movielens",  # 数据库名称
    "charset": "utf8mb4"
}

# 代理配置（如果需要访问Gemini）
PROXY_CONFIG = {
    "http_proxy": "http://127.0.0.1:7890",  # 根据您的代理设置修改
    "https_proxy": "http://127.0.0.1:7890",
    "all_proxy": "socks5://127.0.0.1:7890"
}
