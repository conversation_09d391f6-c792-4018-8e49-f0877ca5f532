#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库初始化脚本
用于创建MovieLens数据库和表结构
"""

import pymysql
import pandas as pd
import os

# 数据库配置
DB_CONFIG = {
    "host": "localhost",
    "user": "root",
    "password": "",  # 请填入您的MySQL密码
    "charset": "utf8mb4"
}

def create_database():
    """创建数据库"""
    try:
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        # 创建数据库
        cursor.execute("CREATE DATABASE IF NOT EXISTS movielens CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        print("✓ 数据库创建成功")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"✗ 数据库创建失败: {e}")

def create_tables():
    """创建表结构"""
    try:
        db_config = DB_CONFIG.copy()
        db_config["database"] = "movielens"
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor()
        
        # 创建movies表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS `movies` (
            `movieId` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
            `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
            `genres` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL
        ) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;
        """)
        
        # 创建ratings表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS `ratings` (
            `userId` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
            `movieId` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
            `rating` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
            `timestamp` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL
        ) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;
        """)
        
        # 创建links表
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS `links` (
            `movieId` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
            `imdbId` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
            `tmdbId` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL
        ) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;
        """)
        
        print("✓ 表结构创建成功")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"✗ 表结构创建失败: {e}")

def load_sample_data():
    """加载示例数据"""
    try:
        db_config = DB_CONFIG.copy()
        db_config["database"] = "movielens"
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor()
        
        # 插入示例电影数据
        movies_data = [
            ("1", "Toy Story (1995)", "Adventure|Animation|Children|Comedy|Fantasy"),
            ("2", "Jumanji (1995)", "Adventure|Children|Fantasy"),
            ("3", "Grumpier Old Men (1995)", "Comedy|Romance"),
            ("4", "Waiting to Exhale (1995)", "Comedy|Drama|Romance"),
            ("5", "Father of the Bride Part II (1995)", "Comedy")
        ]
        
        cursor.executemany(
            "INSERT IGNORE INTO movies (movieId, title, genres) VALUES (%s, %s, %s)",
            movies_data
        )
        
        # 插入示例评分数据
        ratings_data = [
            ("1", "1", "4.0", "964982703"),
            ("1", "3", "4.0", "964981247"),
            ("1", "6", "4.0", "964982224"),
            ("2", "1", "3.5", "964982931"),
            ("2", "2", "3.0", "964982400")
        ]
        
        cursor.executemany(
            "INSERT IGNORE INTO ratings (userId, movieId, rating, timestamp) VALUES (%s, %s, %s, %s)",
            ratings_data
        )
        
        # 插入示例链接数据
        links_data = [
            ("1", "0114709", "862"),
            ("2", "0113497", "8844"),
            ("3", "0113228", "15602"),
            ("4", "0114885", "31357"),
            ("5", "0113041", "11862")
        ]
        
        cursor.executemany(
            "INSERT IGNORE INTO links (movieId, imdbId, tmdbId) VALUES (%s, %s, %s)",
            links_data
        )
        
        connection.commit()
        print("✓ 示例数据加载成功")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"✗ 示例数据加载失败: {e}")

def main():
    """主函数"""
    print("MovieLens数据库初始化")
    print("=" * 30)
    
    if not DB_CONFIG["password"]:
        print("⚠️  请先在脚本中填入您的MySQL密码")
        return
    
    create_database()
    create_tables()
    load_sample_data()
    
    print("\n✓ 数据库初始化完成！")
    print("注意：这只是示例数据，请从以下地址下载完整的MovieLens数据集：")
    print("https://files.grouplens.org/datasets/movielens/ml-latest.zip")

if __name__ == "__main__":
    main()
