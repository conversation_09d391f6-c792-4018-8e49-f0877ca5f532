[{"sql": "SELECT book_name FROM ebooks  \nWHERE book_id IN (  \n  SELECT book_id FROM reader_records  \n  GROUP BY book_id  \n  ORDER BY AVG(read_count) DESC  \n  LIMIT 10  \n);  ", "question": "查询平均阅读量最高的前10本电子书名称"}, {"sql": "SELECT product_name FROM (  \n  SELECT p.product_name, AVG(o.amount) avg_amount,  \n    RANK() OVER(ORDER BY AVG(o.amount) DESC) rk  \n  FROM products p  \n  JOIN orders o USING(product_id)  \n  GROUP BY p.product_id  \n) t WHERE rk <=10;  ", "question": "筛选订单平均金额最高的前10个商品"}, {"sql": "SELECT sensor_id FROM (  \n  SELECT sensor_id, STDDEV_POP(value) std_value  \n  FROM sensor_data  \n  GROUP BY sensor_id  \n  ORDER BY std_value ASC  \n  LIMIT 10  \n);  ", "question": "获取实验数据标准差最小的前10个传感器编号"}, {"sql": "SELECT customer_id FROM product_comments  \nGROUP BY customer_id  \nORDER BY COUNT(comment_id) DESC  \nLIMIT 1;  ", "question": "查找发表评论次数最多的顾客编号"}, {"sql": "SELECT door_number FROM maintenance_records  \nGROUP BY door_number  \nHAVING COUNT(*) = (  \n  SELECT MAX(cnt) FROM (  \n    SELECT COUNT(*) cnt FROM maintenance_records GROUP BY door_number  \n  ) t  \n);  ", "question": "确定提交报修申请最频繁的住户门牌号"}, {"sql": "SELECT scholar_id FROM (  \n  SELECT scholar_id, COUNT(*) cites,  \n    ROW_NUMBER() OVER(ORDER BY COUNT(*) DESC) rn  \n  FROM citation_records  \n  GROUP BY scholar_id  \n) WHERE rn=1;", "question": "统计论文被引用次数最多的学者工号"}, {"sql": "SELECT p.product_name, c.certification_no  \nFROM products p  \nJOIN certifications c USING(product_id)  \nWHERE p.category = '智能家居';  ", "question": "显示所有\"智能家居\"类产品的名称及3C认证编号"}, {"sql": "SELECT f.food_name, b.batch_number  \nFROM foods f  \nJOIN production_batches b USING(food_id)  \nWHERE FIND_IN_SET('有机认证', REPLACE(f.tags, '|', ',')) >0; ", "question": "检索含有\"有机认证\"标签的食品名称及批次号"}, {"sql": "SELECT ticket_id, handler_id FROM work_orders  \nWHERE priority_level REGEXP '(^|,)紧急(,|$)';  ", "question": "获取所有\"紧急\"级别工单的编号及处理人员ID"}, {"sql": "SELECT app_name,  \n  AVG(daily_active_users) avg_dau,  \n  COUNT(log_date) active_days  \nFROM app_usage  \nGROUP BY app_id;", "question": "统计每款APP的日活均值及活跃天数"}, {"sql": "SELECT station_code,  \n  ROUND(AVG(temperature),1) avg_temp,  \n  COUNT(record_time) readings  \nFROM weather_data  \nGROUP BY station_id; ", "question": "计算每个气象站的温度平均值及记录次数"}, {"sql": "SELECT outlet_name,  \n  AVG(TIMESTAMPDIFF(HOUR, send_time, receive_time)) avg_hours,  \n  COUNT(tracking_id) total_orders  \nFROM delivery_records  \nGROUP BY outlet_id;", "question": "生成每个快递网点的派件时效报告"}, {"sql": "SELECT isbn FROM library_books  \nWHERE book_id NOT IN (  \n  SELECT DISTINCT book_id FROM borrowing_records  \n);", "question": "查询从未被借阅过的图书ISBN"}, {"sql": "SELECT supplier_id FROM suppliers  \nWHERE NOT EXISTS (  \n  SELECT 1 FROM complaint_records  \n  WHERE supplier_id = suppliers.supplier_id  \n);", "question": "筛选零投诉记录的供应商编号"}, {"sql": "SELECT batch_no FROM inventory  \nLEFT JOIN outbound_records USING(batch_id)  \nWHERE outbound_id IS NULL;", "question": "检索未被扫描出库的货物批次"}, {"sql": "SELECT factory_name FROM factories  \nWHERE iso9001_cert IS NOT NULL  \n  AND iso14001_cert IS NOT NULL;", "question": "查找同时具备ISO9001和ISO14001认证的工厂"}, {"sql": "SELECT shop_name FROM business_licenses bl  \nJOIN hygiene_permits hp USING(shop_id)  \nWHERE bl.license_no IS NOT NULL  \n  AND hp.permit_no IS NOT NULL;", "question": "获取同时拥有营业执照和卫生许可证的商铺"}, {"sql": "SELECT user_name FROM users  \nWHERE identity_verified = 1  \n  AND bankcard_bound = 1;", "question": "筛选已完成身份认证和银行卡绑定的用户"}, {"sql": "SELECT movie_title FROM cinema_movies \nWHERE movie_id IN ( \n  SELECT movie_id FROM audience_ratings \n  GROUP BY movie_id \n  ORDER BY AVG(score) DESC \n  LIMIT 15 \n);", "question": "查询观众评分平均值最高的前15部电影名称"}, {"sql": "SELECT course_name FROM online_courses \nWHERE course_id IN ( \n  SELECT course_id FROM student_evaluations \n  GROUP BY course_id \n  ORDER BY AVG(rating) DESC \n  LIMIT 8 \n);", "question": "筛选学生评价平均分最高的前8门课程"}, {"sql": "SELECT customer_id FROM service_feedback \nGROUP BY customer_id \nORDER BY COUNT(feedback_id) DESC \nLIMIT 1;", "question": "查找提交服务反馈次数最多的客户编号"}, {"sql": "SELECT employee_id FROM project_assignments \nGROUP BY employee_id \nHAVING COUNT(*) = ( \n  SELECT MAX(cnt) FROM ( \n    SELECT COUNT(*) cnt FROM project_assignments GROUP BY employee_id \n  ) t \n);", "question": "确定参与项目数量最多的员工工号"}, {"sql": "SELECT device_name, certification_code \nFROM electronic_devices \nJOIN quality_certifications USING(device_id) \nWHERE device_category = '智能手机';", "question": "显示所有\"智能手机\"类设备的名称及质量认证编号"}, {"sql": "SELECT medicine_name, batch_code \nFROM pharmaceuticals \nJOIN production_records USING(medicine_id) \nWHERE FIND_IN_SET('进口药品', REPLACE(tags, '|', ',')) > 0;", "question": "检索含有\"进口药品\"标签的药品名称及批次号"}, {"sql": "SELECT game_title, \n  AVG(daily_players) avg_players, \n  COUNT(record_date) active_days \nFROM game_statistics \nGROUP BY game_id;", "question": "统计每款游戏的日均玩家数及活跃天数"}, {"sql": "SELECT restaurant_name, \n  ROUND(AVG(service_score),1) avg_service, \n  COUNT(review_id) total_reviews \nFROM restaurant_reviews \nGROUP BY restaurant_id;", "question": "计算每家餐厅的服务平均分及评价次数"}, {"sql": "SELECT product_code FROM warehouse_inventory \nWHERE product_id NOT IN ( \n  SELECT DISTINCT product_id FROM sales_records \n);", "question": "查询从未被销售过的产品编码"}, {"sql": "SELECT teacher_id FROM teachers \nWHERE NOT EXISTS ( \n  SELECT 1 FROM student_complaints \n  WHERE teacher_id = teachers.teacher_id \n);", "question": "筛选零投诉记录的教师编号"}]